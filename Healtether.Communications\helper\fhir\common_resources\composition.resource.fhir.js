import { v4 as uuidv4 } from "uuid";
import {
  compositionDiv,
  compositionOPConsultMetadata,
  clinicalConsultationReport,
  allergies,
  chiefComplaints,
  medicalHistory,
  familyHistory,
  observations,
  investigationAdvice,
  medications,
  procedure,
  documentReference,
  appointment,
  compositionPrescriptionMetadata,
  prescriptionReport,
  bundleIdentifier,
  prescriptionReportWithEntry,
  compositionWellnessMetadata,
  wellnessReport,
  compositionHealthDocumentMetadata,
  healthDocumentReport,
  healthDocumentReportWithEntry,
  compositionImmunizationMetadata,
  immunizationReport,
  compositionDiagnosticReportMetadata,
  diagnosticReport,
  compositionInvoiceMetadata,
  invoiceReport,
} from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

 
export const generateInvoiceComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  encounterResource,
  practitionerResources,
  organizationResource,
  invoiceResources,
  chargeItemResources,
  binaryResource,
  documentReferenceResources = []

) => {
  const reportTitle = "Invoice Record";
  const id = uuidv4();

  const compositionMetadata = compositionInvoiceMetadata(currentTime);
  const compositionType = invoiceReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (encounterResource) {
    optionalFields.encounter = {
      reference: `urn:uuid:${encounterResource.resource.id}`,
      display: encounterResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }

  const section = [
    {
      title: "Invoice Details",
      // code: {
      //   coding: [
      //     {
      //       system: "http://loinc.org",
      //       code: "55107-7",
      //       display: "Invoice Record",
      //     },
      //   ],
      // },
      entry: [
        // Include Invoice resource only if provided - ABDM InvoiceRecord profile only allows Invoice entries
        ...(invoiceResources ? [{
          reference: `urn:uuid:${invoiceResources.resource.id}`,
          type: "Invoice",
        }] : [])
      ],
    }
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generateDiagnosticReportComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  encounterResource,
  practitionerResources,
  organizationResource,
  diagnosticReportResources,
  documentReferenceResources
) => {
  const reportTitle = "Diagnostic Report";
  const id = uuidv4();

  const compositionMetadata = compositionDiagnosticReportMetadata(currentTime);
  const compositionType = diagnosticReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (encounterResource) {
    optionalFields.encounter = {
      reference: `urn:uuid:${encounterResource.resource.id}`,
      display: encounterResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }

  console.log("diagnosticReportResources", diagnosticReportResources);
  const categories = diagnosticReportResources.flatMap(
    (item) => item.resource.category
  );
  const section = [
    {
      title: "Diagnostic Reports",
      code: categories[0] || {},
      entry: [
        ...diagnosticReportResources.map((resource) => ({
          reference: `urn:uuid:${resource.resource.id}`,
          type: "DiagnosticReport",
        })),
        ...documentReferenceResources.map((resource) => ({
          reference: `urn:uuid:${resource.resource.id}`,
          type: "DocumentReference",
        })),
      ],
    },
    // documentReference(documentReferenceResources),
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generateOpConsultComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  encounterResource,
  practitionerResources,
  organizationResource,
  allergyIntoleranceResources,
  conditionResources,
  serviceRequestResources,
  medicationStatementResources,
  medicationRequestResources,
  procedureResources,
  documentReferenceResources,
  appointmentResource,
  familyMemberHistoryResources = [],
  observationResources = []
) => {
  const reportTitle = "Consultation Report";
  const id = uuidv4();

  const compositionMetadata = compositionOPConsultMetadata(currentTime);
  const compositionType = clinicalConsultationReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (encounterResource) {
    optionalFields.encounter = {
      reference: `urn:uuid:${encounterResource.resource.id}`,
      display: encounterResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }

  const section = [
    chiefComplaints(conditionResources),
    allergies(allergyIntoleranceResources),
    familyHistory(familyMemberHistoryResources),
    observations(observationResources),
    procedure(procedureResources),
    appointment(
      appointmentResource.resource.id,
      appointmentResource.resource.resourceType
    ),
    investigationAdvice(serviceRequestResources),
    medications(medicationStatementResources, medicationRequestResources),
    documentReference(documentReferenceResources),
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generatePrescriptionComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  encounterResource,
  practitionerResources,
  organizationResource,
  conditionResources,
  medicationRequestResources,
  binaryResource
) => {
  const reportTitle = "Prescription record";
  const id = uuidv4();

  const compositionMetadata = compositionPrescriptionMetadata(currentTime);
  const compositionType = prescriptionReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (encounterResource) {
    optionalFields.encounter = {
      reference: `urn:uuid:${encounterResource.resource.id}`,
      display: encounterResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }

  const section = [
    prescriptionReportWithEntry(
      binaryResource.resource.id,
      toTitleCase(binaryResource.resource.resourceType),
      medicationRequestResources
    ),
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generateWellnessComposition = async (
  general,
  currentTime,
  patientResource,
  practitionerResource,
  observationResources
) => {
  const reportTitle = "Wellness Record";
  const id = uuidv4();

  const compositionMetadata = compositionWellnessMetadata(currentTime);
  const compositionType = wellnessReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (practitionerResource.length > 0) {
    optionalFields.author = practitionerResource.map((practitioner) => ({
      reference: `urn:uuid:${practitioner.resource.id}`,
      display: toTitleCase(practitioner.resource.name[0]?.text),
    }));
  }

  const section = [
    {
      title: "Vital Signs",
      entry: observationResources.map((observation) => ({
        reference: observation.fullUrl,
        display: "ObservationVitalSigns",
      })),
    },
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generateImmunizationComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  encounterResource,
  practitionerResources,
  organizationResource,
  immunizationResources,
  immunizationRecommendationResources,
  documentReferenceResources
) => {
  const reportTitle = "Immunization Record";
  const id = uuidv4();

  const compositionMetadata = compositionImmunizationMetadata(currentTime);
  const compositionType = immunizationReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (encounterResource) {
    optionalFields.encounter = {
      reference: `urn:uuid:${encounterResource.resource.id}`,
      display: encounterResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }
  const entry = [
    ...immunizationResources.map((resource) => ({
      reference: `urn:uuid:${resource.resource.id}`,
      type: "Immunization",
    })),
    ...immunizationRecommendationResources.map((resource) => ({
      reference: `urn:uuid:${resource.resource.id}`,
      type: "ImmunizationRecommendation",
    })),
    ...documentReferenceResources.map((resource) => ({
      reference: `urn:uuid:${resource.resource.id}`,
      type: "DocumentReference",
    })),
  ];
  const section = [
    {
      title: "Immunization record",
      code: {
        coding: [
          {
            system: "http://snomed.info/sct",
            code: "41000179103",
            display: "Immunization record",
          },
        ],
      },
      entry:entry
    }
  ];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

export const generateHealthDocumentComposition = async (
  general,
  currentTime,
  patientResource,
  doctors,
  practitionerResources,
  organizationResource,
  documentReferenceResources
) => {
  const reportTitle = "Health Document";
  const id = uuidv4();

  const compositionMetadata = compositionHealthDocumentMetadata(currentTime);
  const compositionType = healthDocumentReport();

  const optionalFields = {};

  if (patientResource) {
    optionalFields.subject = {
      reference: `urn:uuid:${patientResource.resource.id}`,
      display: patientResource.resource.resourceType,
    };
  }

  if (practitionerResources) {
    const normalize = (str) => str.trim().toLowerCase();
    const normalizedDoctors = doctors.map(normalize);

    optionalFields.author = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        reference: `urn:uuid:${practitioner.resource.id}`,
        display: toTitleCase(practitioner.resource.name[0]?.text),
      }));

    optionalFields.attester = practitionerResources
      .filter((practitioner) =>
        practitioner.resource.name.some((nameObj) =>
          normalizedDoctors.includes(normalize(nameObj.text))
        )
      )
      .map((practitioner) => ({
        mode: "legal",
        party: {
          reference: `urn:uuid:${practitioner.resource.id}`,
          display: toTitleCase(practitioner.resource.name[0]?.text),
        },
      }));
  }

  if (organizationResource) {
    optionalFields.custodian = {
      reference: `urn:uuid:${organizationResource.resource.id}`,
      display: toTitleCase(organizationResource.resource.name),
    };

    optionalFields.attester = [
      ...(optionalFields.attester || []),
      {
        mode: "legal",
        party: {
          reference: `urn:uuid:${organizationResource.resource.id}`,
          display: toTitleCase(organizationResource.resource.name),
        },
      },
    ];
  }

  const section = [healthDocumentReportWithEntry(documentReferenceResources)];

  return await generateComposition(
    id,
    compositionMetadata,
    general,
    compositionType,
    currentTime,
    reportTitle,
    section,
    optionalFields
  );
};

const generateComposition = async (
  id,
  compositionMetadata,
  general,
  compositionType,
  currentTime,
  reportTitle,
  section,
  optionalFields = {}
) => {
  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Composition",
      id,
      meta: compositionMetadata,
      identifier: bundleIdentifier(general.hipUrl, general.clientId),
      language: "en",
      status: general.status,
      type: compositionType,
      date: currentTime,
      title: reportTitle,
      ...optionalFields,
      section
      // text: compositionDiv(),
    },
  };
};
