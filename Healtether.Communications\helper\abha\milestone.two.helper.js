import { v4 as uuidv4 } from "uuid";
import moment from "moment";
import { AbdmAccessToken } from "../abha/abdm-access-token.js";
import {
  AbhaResponse,
  CARECONTEXT_ON_INIT,
  HIP_ON_NOTIFY_DATAPUSH,
  LINK_CARE_CONTEXT,
  LINKING_TOKEN,
  NOTIFY_HEALTHINFORMATION_HIU,
  ON_CONFIRM,
  ON_DISCOVER,
  ON_REQUEST,
} from "../../utils/abha.api.js";
import portalDb from "../../config/clinics.collections.config.js";
import { OPConsultRecordSchema } from "../../schema/fhir_schema/op_consult.schema.fhir.js";
import { PrescriptionRecordSchema } from "../../schema/fhir_schema/prescription.schema.fhir.js";
import fs from "fs";
import path from "path";
import { HealthDocumentRecordSchema } from "../../schema/fhir_schema/health_document.schema.fhir.js";
import { WellnessRecordSchema } from "../../schema/fhir_schema/wellness.schema.fhir.js";
import { InvoiceRecordSchema } from "../../schema/fhir_schema/invoice.schema.fhir.js";
import { generateOTP, sendSMSMessage, sns } from "../../utils/awsOTP.js";
const AbhaConsentsModel = portalDb.model("abhaConsents");
const PatientsModel = portalDb.model("Patient");
const base_url = process.env.ABHA_M2_BASE_URL;

// export const getPatientData = async (req, res) => {
//   const patientId = req.params.id;
//   const patientData = await PatientsModel.findById(patientId);
//   res.status(200).json({ patientData });
// };

export const linkingToken = async (body, hipId, cmId) => {
  console.log(body, hipId, cmId);
  const result = await fetchAbhaApi(LINKING_TOKEN, body, "POST", {
    "X-CM-ID": cmId,
    "X-HIP-ID": hipId,
  });
  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};
export const linkCareContext = async (body, hipId, cmId, linkingToken) => {
  const result = await fetchAbhaApi(LINK_CARE_CONTEXT, body, "POST", {
    "X-CM-ID": cmId,
    "X-HIP-ID": hipId,
    "X-LINK-TOKEN": linkingToken,
  });
  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};


export const discoverCareContext = async (data, requestId) => {
  let patientData = data.patient;
  let Document = [];
  let obj = {};
  const nameParts = patientData.name.trim().split(" ");
  if (nameParts.length > 0) {
    obj.firstName = { $regex: new RegExp(nameParts[0], "i") };
  }
  if (nameParts.length > 1) {
    obj.lastName = { $regex: new RegExp(nameParts.slice(1).join(" "), "i") };
  }
  obj.mobile =
    patientData.verifiedIdentifiers[0].type == "MOBILE"
      ? patientData.verifiedIdentifiers[0].value?.toString()
      : patientData.verifiedIdentifiers[1].value;
  if (patientData.gender) {
    obj.gender = patientData.gender == "M" ? "Male" : "Female";
  }
  if (patientData.yearOfBirth) {
    const currentYear = new Date().getFullYear();
    const calculatedAge = currentYear - patientData.yearOfBirth;
    obj.age = { $gte: calculatedAge - 2, $lte: calculatedAge + 2 };
  }
  let Exist = await PatientsModel.findOne(obj);
  console.log("exist", Exist);
  if (Exist) {
    Exist.abhaAddress = patientData.id;
    Exist.abhaNumber =
      patientData.verifiedIdentifiers[0].type == "ABHA_NUMBER"
        ? patientData.verifiedIdentifiers[0].value?.toString()
        : patientData.verifiedIdentifiers[1].value;
    await Exist.save();
    console.log("Patient ABHA updated successfully");


    const WellnessRecordModel = portalDb.model(
      "WellnessReportFHIRRecord",
      WellnessRecordSchema
    );
    const HealthDocumentRecordModel = portalDb.model(
      "HealthDocumentFHIRRecord",
      HealthDocumentRecordSchema
    );
    const OPConsultRecordModel = portalDb.model(
      "OPConsultFHIRRecord",
      OPConsultRecordSchema
    );

    const PrescriptionRecordModel = portalDb.model(
      "PrescriptionFHIRRecord",
      PrescriptionRecordSchema
    );
    const InvoiceRecordModel = portalDb.model(
      "InvoiceReportFHIRRecord",
      InvoiceRecordSchema
    );
    // let invoicerecords = await InvoiceRecordModel.findOne({
    //   "patient.id": Exist._id,
    // });
    const updatedInvoiceRecord = await InvoiceRecordModel.findOneAndUpdate(
      { "patient.id": Exist._id },
      { 
        $set: {
          "patient.abhaAddress": Exist.abhaAddress,
          "patient.abhaNumber": Exist.abhaNumber,
      }},
      { new: true } // This option returns the updated document instead of the original
    );
    if (updatedInvoiceRecord) {
      Document.push(updatedInvoiceRecord);
    }
    // let wellnessrecords = await WellnessRecordModel.findOne({
    //   "patient.id": Exist._id,
    // });
    const updatedWellnessRecords = await WellnessRecordModel.findOneAndUpdate(
      { "patient.id": Exist._id },
      { 
        $set: {
          "patient.abhaAddress": Exist.abhaAddress,
          "patient.abhaNumber": Exist.abhaNumber,
      }},
      { new: true } // This option returns the updated document instead of the original
    );
    if (updatedWellnessRecords) {
      Document.push(updatedWellnessRecords);
    }
    // let healthrecords = await HealthDocumentRecordModel.findOne({
    //   "patient.id": Exist._id,
    // });
   
    const updatedHealthrecords = await HealthDocumentRecordModel.findOneAndUpdate(
      { "patient.id": Exist._id },
      { 
        $set: {
          "patient.abhaAddress": Exist.abhaAddress,
          "patient.abhaNumber": Exist.abhaNumber,
      }},
      { new: true } // This option returns the updated document instead of the original
    );
    if (updatedHealthrecords) {
      Document.push(updatedHealthrecords);
    }
    // let oprecords = await OPConsultRecordModel.findOne({
    //   "patient.id": Exist._id,
    // });
    const updatedOprecords = await OPConsultRecordModel.findOneAndUpdate(
      { "patient.id": Exist._id },
      { 
        $set: {
          "patient.abhaAddress": Exist.abhaAddress,
          "patient.abhaNumber": Exist.abhaNumber,
      }},
      { new: true } // This option returns the updated document instead of the original
    );
    if (updatedOprecords) {
      Document.push(updatedOprecords);
    }
    // let prescriptionrecords = await PrescriptionRecordModel.findOne({
    //   "patient.id": Exist._id,
    // });

    const updatedPrescriptionrecords = await PrescriptionRecordModel.findOneAndUpdate(
      { "patient.id": Exist._id },
      { 
        $set: {
          "patient.abhaAddress": Exist.abhaAddress,
          "patient.abhaNumber": Exist.abhaNumber,
      }},
      { new: true } // This option returns the updated document instead of the original
    );
    if (updatedPrescriptionrecords) {
      Document.push(updatedPrescriptionrecords);
    }
    console.log("Document", Document);
  }

  let carecontext = Document.map((data) => {
    // // console.log("data1212", Object.keys(data._doc));
    // // console.log("Full document:", JSON.stringify(data, null, 2));
    // let data=Data?._doc;
    return {
      referenceNumber: data?.patient?.id,
      display: data.general.artifact,
      careContexts: [
        {
          referenceNumber: data.fhirId,
          display: `${data.general.artifact}--${moment(new Date()).format(
            "YYYY-MM-DD:HH:mm:ss"
          )}`,
        },
      ],
      hiType:
        data.general.artifact == "WellnessRecord"
          ? "WellnessRecord"
          : data.general.artifact == "InvoiceRecord"
          ? "Invoice"
          : data.general.artifact == "HealthDocumentRecord"
          ? "HealthDocumentRecord"
          : data.general.artifact == "OPConsultRecord"
          ? "OPConsultation"
          : data.general.artifact == "dischargeSummary"
          ? "DischargeSummary"
          : data.general.artifact == "PrescriptionRecord"
          ? "Prescription"
          : "",
      count: 1,
    };
  });

  console.log("carecontext", carecontext);

  let body = {
    transactionId: data.transactionId,
    patient: carecontext,
    matchedBy: ["MR"],
    response: {
      requestId: requestId,
    },
  };
  const filePath = path.join("./", "discoverCareContextData.json");
  fs.writeFileSync(filePath, JSON.stringify(body, null, 2), "utf-8");
  console.log("body", body);
  const result = await fetchAbhaApi(ON_DISCOVER, body, "POST", {
    "X-CM-ID": "Sbx" || cmId,
  });

  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  } else {
    const errorText = await result.text();
    console.error("Error response:", errorText); // Log the error response
    console.log(await result.json()); //if the response is JSON
  }
  return new AbhaResponse(result.ok, responseBody);
};

const transactionTracker = {
  // Store all transaction data by linkRefNumber
  transactions: new Map(),

  // Save transaction data with linkRefNumber as the key
  saveTransaction(linkRefNumber, data) {
    this.transactions.set(linkRefNumber, {
      ...data,
      createdAt: new Date(),
    });
  },

  // Get transaction data by linkRefNumber
  getByLinkRefNumber(linkRefNumber) {
    return this.transactions.get(linkRefNumber);
  },

  // Remove transaction data after processing
  removeTransaction(linkRefNumber) {
    this.transactions.delete(linkRefNumber);
  },

  // Clean up old transactions (called periodically)
  cleanupOldTransactions() {
    const now = new Date();
    for (const [linkRefNumber, data] of this.transactions.entries()) {
      // Remove transactions older than 10 minutes
      if (now - data.createdAt > 10 * 60 * 1000) {
        this.transactions.delete(linkRefNumber);
      }
    }
  },
};

const otp = generateOTP();

export const onInitCareContext = async (data, requestId) => {
  const linkRefNumber = uuidv4(); // In production use: uuid.v4()

  // Save the transaction data with the linkRefNumber as the key
  transactionTracker.saveTransaction(linkRefNumber, {
    transactionId: data.transactionId,
    requestId: requestId,
    abhaAddress: data.abhaAddress,
    patient: data.patient,
  });
  let expiryTime = moment().add(5, "minutes").toISOString();
  let body = {
    transactionId: data.transactionId,
    link: {
      referenceNumber: linkRefNumber,
      authenticationType: " MEDIATE",
      meta: {
        communicationMedium: "MOBILE",
        communicationHint: "OTP",
        communicationExpiry: expiryTime,
      },
    },
    response: {
      requestId: requestId,
    },
  };
  const result = await fetchAbhaApi(CARECONTEXT_ON_INIT, body, "POST", {
    "X-CM-ID": "sbx" || cmId,
  });

  const patientData = await PatientsModel.findOne({
    abhaAddress: data.abhaAddress,
  });

  const number = `+91${patientData.mobile}`;
  console.log("phoneNumber", number);

  (async () => {
    try {
      const params = {
        Message: `Your OTP code is: ${otp}`,
        PhoneNumber: number,
        MessageAttributes: {
          "AWS.SNS.SMS.SenderID": {
            DataType: "String",
            StringValue: "Healtether",
          },
        },
      };
      const q = await sendSMSMessage(sns, params);
    } catch (error) {
      console.log("💥💥💥💥💥💥💥", error);
    }
  })();

  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};

export const onConfirm = async (data, requestId) => {
  const { confirmation } = data;
  const token = confirmation?.token;
  if (otp !== token) throw new Error("Invalid Otp");
  const linkRefNumber = confirmation?.linkRefNumber;
  console.log(linkRefNumber);
  if (!linkRefNumber) {
    throw new Error("No linkRefNumber found in confirmation data");
  }

  const transactionData = transactionTracker.getByLinkRefNumber(linkRefNumber);
  console.log("transactionData", transactionData);

  const body = {
    patient: transactionData.patient?.map((data) => {
      return {
        careContexts: [
          {
            display: `${data.hiType}-${moment(new Date()).format(
              "YYYY-MM-DD:HH:mm:ss"
            )}`,
            referenceNumber: data.careContexts[0].referenceNumber,
          },
        ],
        count: 1,
        display: `${data.hiType}-${moment(new Date()).format(
          "YYYY-MM-DD:HH:mm:ss"
        )}`,
        hiType: data.hiType,
        referenceNumber: data.referenceNumber,
      };
    }),
    response: {
      requestId: requestId,
    },
  };

  const filePath = path.join("./", "onConfirmCareContextData.json");
  fs.writeFileSync(filePath, JSON.stringify(body, null, 2), "utf-8");

  const result = await fetchAbhaApi(ON_CONFIRM, body, "POST", {
    "X-CM-ID": "sbx",
  });

  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;

    // Update the linked flag in each document from the Document array
    for (const patientDoc of transactionData.patient) {
      const modelType = getModelTypeFromHiType(patientDoc.hiType);
      const recordId = patientDoc.careContexts[0].referenceNumber;

      if (modelType) {
        try {
          // Get the appropriate model
          const Model = getModelFromType(modelType);

          // Update the document to set linked flag to true
          await Model.findOneAndUpdate(
            { fhirId: { $regex: recordId + "$" } }, // Match the document by fhirId ending with recordId
            { $set: { abhaCareContextLinked: true } },
            { new: true }
          );

          console.log(
            `Successfully updated linked flag for ${modelType} with ID: ${recordId}`
          );
        } catch (error) {
          console.error(`Error updating linked flag for ${modelType}:`, error);
        }
      }
    }

    // Clean up the transaction data
    transactionTracker.cleanupOldTransactions();
  } else {
    const response = await result.json();
    console.log("response1234", response);
  }
  return new AbhaResponse(result.ok, responseBody);
};

// Helper function to get the model type from hiType
function getModelTypeFromHiType(hiType) {
  switch (hiType) {
    case "WellnessRecord":
      return "WellnessReportFHIRRecord";
    case "Invoice":
      return "InvoiceReportFHIRRecord";
    case "HealthDocumentRecord":
      return "HealthDocumentFHIRRecord";
    case "OPConsultation":
      return "OPConsultFHIRRecord";
    case "Prescription":
      return "PrescriptionFHIRRecord";
    case "DischargeSummary":
      return "DischargeSummaryFHIRRecord";
    default:
      return null;
  }
}

// Helper function to get the model from type
function getModelFromType(modelType) {
  return portalDb.model(modelType, getSchemaFromModelType(modelType));
}

// Helper function to get the schema based on model type
function getSchemaFromModelType(modelType) {
  switch (modelType) {
    case "WellnessReportFHIRRecord":
      return WellnessRecordSchema;
    case "InvoiceReportFHIRRecord":
      return InvoiceRecordSchema;
    case "HealthDocumentFHIRRecord":
      return HealthDocumentRecordSchema;
    case "OPConsultFHIRRecord":
      return OPConsultRecordSchema;
    case "PrescriptionFHIRRecord":
      return PrescriptionRecordSchema;
    case "DischargeSummaryFHIRRecord":
      return DischargeSummaryRecordSchema;
    default:
      throw new Error(`Unknown model type: ${modelType}`);
  }
}
export const onNotifyConsentM2 = async (consentId, headers) => {
  const cmId = headers["x-cm-id"] || "sbx";
  let requestId = headers["request-id"];
  let body = {
    acknowledgement: {
      status: "OK",
      consentId: consentId,
    },
    error: {
      code: "ABDM-1001",
      message: "unable to connect database",
    },
    response: {
      requestId: requestId,
    },
  };

  const result = await fetchAbhaApi(HIP_ON_NOTIFY_DATAPUSH, body, "POST", {
    "X-CM-ID": cmId || "sbx",
  });
  let responseBody;
  if (result.ok) {
    await AbhaConsentsModel.updateOne(
      { consentId: consentId },
      {
        $set: {
          requestId: result.requestId,
        },
      }
    );
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};

export const onRequestM2 = async (data) => {
console.log("data on-request", data);
  
  const requestBody = {
    hiRequest: {
      sessionStatus: "ACKNOWLEDGED",
      transactionId: data.transactionId,
    },
    error: {
      code: "ABDM-1001",
      message: "unable to connect database",
    },
    response: {
      requestId: data.requestId,
    },
  };
  const result = await fetchAbhaApi(ON_REQUEST, requestBody, "POST", {
    "X-CM-ID": "sbx",
  });

  console.log("result on-request", result);
  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};

export const transferData = async (data, url) => {
  const result = await fetchAbhaApi(
    url,
    data,
    "POST",
    {
      "X-CM-ID": "sbx",
    },
    url
  );
  let responseBody;
  if (result.ok) {
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};

export const informHUI = async (body) => {
  const result = await fetchAbhaApi(
    NOTIFY_HEALTHINFORMATION_HIU,
    body,
    "POST",
    { "X-CM-ID": "sbx" }
  );
  let responseBody;
  if (result.ok) {
    console.log("result data push status", result);
    responseBody =
      result?.headers?.get("content-length") > 0 ? await result.json() : null;
  }
  return new AbhaResponse(result.ok, responseBody);
};

export const notifyContextM2 = async (consentId, headers) => {
  try {
    const cmId = headers["x-cm-id"] || "sbx";

    let body = {
      notification: {
        patient: {
          id: data.notification.consentDetail.patient || "user_122@sbx",
        },
        careContext: data.notification.consentDetail.careContext || {
          patientReference: "batman@tmh",
          careContextReference: "Episode1",
        },
        hiTypes: data.notification.consentDetail.hiTypes || ["OPConsultation"],
        date:
          data.notification.consentDetail.permission.dataEraseAt ||
          "2024-05-30T05:21:34.155Z",
        hip: data.notification.consentDetail.hip || { id: "demo-hip-261222" },
      },
    };

    const result = await fetchAbhaApi(CARECONTEXT_ON_INIT, body, "POST", {
      "X-CM-ID": cmId || "sbx",
    });

    let responseBody;
    if (result.ok) {
      responseBody =
        result?.headers?.get("content-length") > 0 ? await result.json() : null;
    }
    return new AbhaResponse(result.ok, responseBody);
  } catch (error) {
    console.error("Error:", error);
    res.status(400).json({ error: error.message, action: true });
  }
};

export const fetchAbhaApi = async (url, body, method, header = {}, pushurl) => {
  try {
    const token = await AbdmAccessToken();
    console.log("token", token);
    const isoTimestamp = new Date().toISOString();
    const randomUUID = uuidv4();

    console.log("randomUUID", randomUUID);
    const defaultHeaders = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
      TIMESTAMP: isoTimestamp,
      "REQUEST-ID": randomUUID,
      Accept: "*/*",
      Connection: "keep-alive",
    };

    const finalHeaders = { ...defaultHeaders, ...header };

    const requestOptions = {
      method: method || "POST",
      headers: finalHeaders,
    };
    console.log(body);

    if (body && method !== "GET" && method !== "HEAD") {
      requestOptions.body = JSON.stringify(body);
    }
    let URL = pushurl ? pushurl : `${base_url}${url}`;
    const result = await fetch(`${URL}`, requestOptions);
    console.log(`${base_url}${url}`, result.ok);
    console.log(await result.text());

    return {
      ok: result.ok,
      status: result.status,
      statusText: result.statusText,
      headers: result.headers,
      url: result.url,
      requestId: randomUUID, // Correctly adding requestId
      json: async () => result.json(), // Retain json() function
      text: async () => result.text(), // Retain text() function
    };
  } catch (error) {
    console.error("Error in fetchAbhaApi:", error);
    throw error;
  }
};
